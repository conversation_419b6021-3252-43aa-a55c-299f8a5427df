use crate::config::DwTestItemConfig;
use crate::cpu_limit::get_current_thread_count;
use bumpalo::Bump;
use ck_provider::{Async<PERSON>k<PERSON>ender, CkProviderError};
use common::ads::sink::test_item_bin_handler::TestItemBinHandler;
use common::ads::sink::test_item_program_handler::TestItemProgramHandler;
use common::ads::sink::test_item_site_bin_handler::TestItemSiteBinHandler;
use common::ads::sink::test_item_site_handler::TestItemSiteHandler;
use common::ck::ck_sink::SinkHandler;
use common::dto::ads::value::test_item_bin::TestItemBin;
use common::dto::ads::value::test_item_detail::TestItemDetail;
use common::dto::ads::value::test_item_program::TestItemProgram;
use common::dto::ads::value::test_item_site::TestItemSite;
use common::dto::ads::value::test_item_site_bin::TestItemSiteBin;
use common::dto::ads::{TestItemBinKey, TestItemProgramKey, TestItemSiteBinKey, TestItemSiteKey};
use common::dto::dwd::file_detail::{FileDetail, SubFileDetail};
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::test_area::TestArea;
use common::model::constant::P;
use common::utils::sink::init_clickhouse_stream_writer;
use dashmap::DashMap;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::{Arc, Mutex};
use tokio::task::JoinHandle;
use tokio::time::Duration;

/// Combined vectors for both total and pass-only records
/// This structure optimizes memory usage by storing both types in a single container
#[derive(Default, Debug)]
struct GroupedVecs {
    total: Vec<Arc<TestItemDetail>>,
    pass: Vec<Arc<TestItemDetail>>,
}

/// Grouped test item details for different aggregation levels
/// Contains both total and pass-only groups for performance optimization
/// Uses the optimized GroupedVecs structure to reduce memory overhead
#[derive(Debug)]
struct GroupedTestItemDetails {
    /// Groups for TestItemProgram aggregation (by program key) - combined total and pass
    program_groups: HashMap<TestItemProgramKey, GroupedVecs>,

    /// Groups for TestItemSite aggregation (by site key) - combined total and pass
    site_groups: HashMap<TestItemSiteKey, GroupedVecs>,

    /// Groups for TestItemBin aggregation (by bin key) - combined total and pass
    bin_groups: HashMap<TestItemBinKey, GroupedVecs>,

    /// Groups for TestItemSiteBin aggregation (by site-bin key) - combined total and pass
    site_bin_groups: HashMap<TestItemSiteBinKey, GroupedVecs>,
}

/// ADS YMS Test Item Service handles transformation from DWD to ADS layer
/// This service processes test item data for YMS (Yield Management System) analytics
///
/// Corresponds to the ADS layer processing in the original Scala implementation
#[derive(Debug, Clone)]
pub struct AdsYmsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
    data_source: String,
    is_cp: bool,
}

impl AdsYmsTestItemService {
    pub fn new(properties: DwTestItemConfig, data_source: String, test_area: TestArea) -> Self {
        let is_cp = TestArea::get_cp_list().contains(&test_area);
        Self { properties, data_source, is_cp }
    }

    /// Calculate test item processing - main entry point
    ///
    /// Corresponds to the Scala calculateTestItem method:
    /// def calculateTestItem(spark: SparkSession, subTestItem: Dataset[SubTestItemDetail],
    ///                      fileDetailMap: Broadcast[Map[Long, FileDetail]],
    ///                      productList: Broadcast[List[OdsProductConfig]],
    ///                      dieFinalInfo: Broadcast[List[DieFinalInfo]],
    ///                      version: Long, itemHasFinalInfo: Boolean): Unit
    pub async fn calculate_test_item(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        product_list: &[OdsProductConfig],
        version: i64,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 ADS YMS Test Item...");

        let sub_file_detail_map = file_detail_map
            .iter()
            .map(|(file_id, file_detail)| (*file_id, SubFileDetail::from_file_detail(file_detail)))
            .collect::<HashMap<i64, SubFileDetail>>();

        // Transform SubTestItemDetail to TestItemDetail
        let test_item_details: Vec<Arc<TestItemDetail>> =
            // Standard build without die final info
            sub_test_item
                .par_iter()
                .flatten()
                .into_par_iter()
                .filter_map(|elem| {
                    if let Some(file_detail) = sub_file_detail_map.get(&elem.FILE_ID?) {
                        Some(Arc::new(TestItemDetail::build_test_item(&elem, file_detail, version)))
                    } else {
                        log::warn!("File detail not found for FILE_ID: {:?}", elem.FILE_ID);
                        None
                    }
                })
                .collect();
        log::info!("转换Ads TestItemDetail完成，共 {} 条数据", test_item_details.len());
        // Calculate all four aggregation results
        self.calculate_all(test_item_details, product_list).await?;

        Ok(())
    }

    /// Generic helper method for async ClickHouse writes
    ///
    /// This method handles the async writing to ClickHouse with proper error handling
    /// and corresponds to the writeToCk calls in the original Scala implementation
    async fn write_to_ck_generic<T>(
        &self,
        data: Vec<T>,
        handler: impl SinkHandler + Send + Sync,
    ) -> Result<(), Box<dyn Error + Send + Sync>>
    where
        T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
    {
        use common::ck::ck_sink::CkSink;

        if data.is_empty() {
            log::info!("No data to write.");
            return Ok(());
        }

        log::info!("Writing {} records to ClickHouse.", data.len());

        let ck_config = self.properties.get_ck_config(self.properties.ads_db_name.as_str());

        // Write to ClickHouse with partition
        CkSink::write_to_ck(&data, 1, &ck_config, &handler, false).await.map_err(
            |e| -> Box<dyn Error + Send + Sync> {
                log::error!("写入clickhouse 失败: {}, 数据量为: {}", handler.table_name(), data.len());
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            },
        )?;

        log::info!("Successfully wrote {} records to ClickHouse.", data.len());
        Ok(())
    }

    /// Calculate all ADS test item aggregations with multi-threading support
    ///
    /// This method replicates the Scala calculateAll functionality:
    /// def calculateAll(spark: SparkSession, testItemDetail: Dataset[TestItemDetail],
    ///                  productList: Broadcast[List[OdsProductConfig]]):
    ///                  (Dataset[TestItemProgram], Dataset[TestItemSite], Dataset[TestItemBin], Dataset[TestItemSiteBin])
    pub async fn calculate_all(
        &self,
        test_item_details: Vec<Arc<TestItemDetail>>,
        product_list: &[OdsProductConfig],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let start_time = std::time::Instant::now();
        let bump_program = Bump::new();
        let bump_site = Bump::new();
        let bump_bin = Bump::new();
        let bump_site_bin = Bump::new();
        let (program_dash_map, site_dash_map, bin_dash_map, site_bin_dash_map) = self
            .group_test_item_details_concurrent(test_item_details)
            .map_err(|e| -> Box<dyn Error + Send + Sync> {
                log::error!("分组失败: {}", e);
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            })?;

        let program_groups = bump_program.alloc(
            program_dash_map
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
        );
        let site_groups = bump_site.alloc(
            site_dash_map
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
        );
        let bin_groups = bump_bin.alloc(
            bin_dash_map
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
        );
        let site_bin_groups = bump_site_bin.alloc(
            site_bin_dash_map
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
        );
        let group_duration = start_time.elapsed();
        log::info!("分组耗时: {:?}", group_duration);

        // Create handlers for each table
        let test_item_program_handler = TestItemProgramHandler::new(self.properties.ads_db_name.clone());
        let test_item_site_handler = TestItemSiteHandler::new(self.properties.ads_db_name.clone());
        let test_item_bin_handler = TestItemBinHandler::new(self.properties.ads_db_name.clone());
        let test_item_site_bin_handler = TestItemSiteBinHandler::new(self.properties.ads_db_name.clone());

        let ck_config = self.properties.get_ck_config(self.properties.ads_db_name.as_str());

        let program_start = std::time::Instant::now();
        let (sender_program, process_program): (AsyncCkSender<TestItemProgram>, JoinHandle<()>) =
            init_clickhouse_stream_writer::<TestItemProgram>(
                &test_item_program_handler,
                ck_config.clone(),
                get_current_thread_count(),
                self.properties.get_batch_size().unwrap(),
            )
            .await?;

        self.process_program_groups(&program_groups, product_list, sender_program.clone())
            .await?;
        sender_program.send(None).await?;
        process_program.await?;
        log::info!("Program 写ck完成，耗时：[{:?}]", program_start.elapsed());
        drop(bump_program);

        let site_start = std::time::Instant::now();
        let (sender_site, process_site): (AsyncCkSender<TestItemSite>, JoinHandle<()>) =
            init_clickhouse_stream_writer::<TestItemSite>(
                &test_item_site_handler,
                ck_config.clone(),
                get_current_thread_count(),
                self.properties.get_batch_size().unwrap(),
            )
            .await?;

        self.process_site_groups(&site_groups, product_list, sender_site.clone())
            .await?;
        sender_site.send(None).await?;
        process_site.await?;
        log::info!("Site 写ck完成，耗时：[{:?}]", site_start.elapsed());
        drop(bump_site);

        let bin_start = std::time::Instant::now();
        let (sender_bin, process_bin): (AsyncCkSender<TestItemBin>, JoinHandle<()>) =
            init_clickhouse_stream_writer::<TestItemBin>(
                &test_item_bin_handler,
                ck_config.clone(),
                get_current_thread_count(),
                self.properties.get_batch_size().unwrap(),
            )
            .await?;

        self.process_bin_groups(&bin_groups, product_list, sender_bin.clone()).await?;
        sender_bin.send(None).await?;
        process_bin.await?;
        log::info!("Bin 写ck完成, 耗时：[{:?}]", bin_start.elapsed());
        drop(bump_bin);

        let site_bin_start = std::time::Instant::now();
        let (sender, process): (AsyncCkSender<TestItemSiteBin>, JoinHandle<()>) =
            init_clickhouse_stream_writer::<TestItemSiteBin>(
                &test_item_site_bin_handler,
                ck_config.clone(),
                get_current_thread_count(),
                self.properties.get_batch_size().unwrap(),
            )
            .await?;

        self.process_site_bin_groups(&site_bin_groups, product_list, sender.clone())
            .await?;
        sender.send(None).await?;
        process.await?;
        log::info!("SiteBin 写ck完成, 耗时：[{:?}]", site_bin_start.elapsed());
        drop(bump_site_bin);
        log::info!("yms ads calculate_all完成.");

        Ok(())
    }

    /// Ultra-optimized concurrent grouping using borrowed lookups
    /// Implements the "fast path / slow path" pattern to minimize key allocations
    /// 使用 DashMap + Mutex 的并发分组方法
    /// 直接返回单一的 GroupedTestItemDetails，避免后续合并操作
    fn group_test_item_details_concurrent(
        &self,
        test_item_details: Vec<Arc<TestItemDetail>>,
    ) -> Result<
        (
            DashMap<TestItemProgramKey, Mutex<GroupedVecs>>,
            DashMap<TestItemSiteKey, Mutex<GroupedVecs>>,
            DashMap<TestItemBinKey, Mutex<GroupedVecs>>,
            DashMap<TestItemSiteBinKey, Mutex<GroupedVecs>>,
        ),
        Box<dyn Error>,
    > {
        // 使用 DashMap 和 Mutex<Vec> 来支持并发写入
        let program_groups: DashMap<TestItemProgramKey, Mutex<GroupedVecs>> = DashMap::new();
        let site_groups: DashMap<TestItemSiteKey, Mutex<GroupedVecs>> = DashMap::new();
        let bin_groups: DashMap<TestItemBinKey, Mutex<GroupedVecs>> = DashMap::new();
        let site_bin_groups: DashMap<TestItemSiteBinKey, Mutex<GroupedVecs>> = DashMap::new();

        // 并行处理每个 TestItemDetail
        test_item_details.into_par_iter().for_each(|detail| {
            // Create keys for each aggregation level
            let program_key = TestItemProgramKey::from_test_item_detail(&detail, self.is_cp);
            let site_key = TestItemSiteKey::from_test_item_detail(&detail, self.is_cp);
            let bin_key = TestItemBinKey::from_test_item_detail(&detail, self.is_cp);
            let site_bin_key = TestItemSiteBinKey::from_test_item_detail(&detail, self.is_cp);

            // Check if this is a pass record
            let is_pass = detail.HBIN_PF.as_ref() == P;

            // Use entry to atomically insert
            let entry = program_groups.entry(program_key).or_default();
            let mut groups = entry.lock().unwrap();
            if is_pass {
                groups.pass.push(detail.clone());
            }
            groups.total.push(detail.clone());

            let entry = site_groups.entry(site_key).or_default();
            let mut groups = entry.lock().unwrap();
            if is_pass {
                groups.pass.push(detail.clone());
            }
            groups.total.push(detail.clone());

            let entry = bin_groups.entry(bin_key).or_default();
            let mut groups = entry.lock().unwrap();
            if is_pass {
                groups.pass.push(detail.clone());
            }
            groups.total.push(detail.clone());

            let entry = site_bin_groups.entry(site_bin_key).or_default();
            let mut groups = entry.lock().unwrap();
            if is_pass {
                groups.pass.push(detail.clone());
            }
            groups.total.push(detail);
        });

        Ok((program_groups, site_groups, bin_groups, site_bin_groups))
    }

    /// Process Program aggregation groups with optimized structure
    async fn process_program_groups(
        &self,
        grouped_data: &HashMap<TestItemProgramKey, GroupedVecs>,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemProgram>,
    ) -> Result<(), CkProviderError> {
        let (total, pass_only) = tokio::join!(
            self.aggregate_program_groups_from_combined(grouped_data, 0, product_list, sender.clone()),
            self.aggregate_program_groups_from_combined(grouped_data, 1, product_list, sender.clone())
        );
        log::info!("Ads yms program aggregation completed.");
        Ok(())
    }

    /// Process Site aggregation groups with optimized structure
    async fn process_site_groups(
        &self,
        grouped_data: &HashMap<TestItemSiteKey, GroupedVecs>,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemSite>,
    ) -> Result<(), CkProviderError> {
        let (total, pass_only) = tokio::join!(
            self.aggregate_site_groups_from_combined(grouped_data, 0, product_list, sender.clone()),
            self.aggregate_site_groups_from_combined(grouped_data, 1, product_list, sender.clone())
        );
        log::info!("Ads yms site aggregation completed.");
        Ok(())
    }

    /// Process Bin aggregation groups with optimized structure
    async fn process_bin_groups(
        &self,
        grouped_data: &HashMap<TestItemBinKey, GroupedVecs>,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemBin>,
    ) -> Result<(), CkProviderError> {
        let (total, pass_only) = tokio::join!(
            self.aggregate_bin_groups_from_combined(grouped_data, 0, product_list, sender.clone()),
            self.aggregate_bin_groups_from_combined(grouped_data, 1, product_list, sender.clone())
        );
        log::info!("Ads yms bin aggregation completed.");
        Ok(())
    }

    /// Process SiteBin aggregation groups with optimized structure
    async fn process_site_bin_groups(
        &self,
        grouped_data: &HashMap<TestItemSiteBinKey, GroupedVecs>,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemSiteBin>,
    ) -> Result<(), CkProviderError> {
        let (total, pass_only) = tokio::join!(
            self.aggregate_site_bin_groups_from_combined(grouped_data, 0, product_list, sender.clone()),
            self.aggregate_site_bin_groups_from_combined(grouped_data, 1, product_list, sender.clone())
        );
        log::info!("Ads yms site bin aggregation completed.");
        Ok(())
    }

    /// Aggregate Program groups from combined structure
    async fn aggregate_program_groups_from_combined(
        &self,
        groups: &HashMap<TestItemProgramKey, GroupedVecs>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemProgram>,
    ) -> Result<(), CkProviderError> {
        let start_time = std::time::Instant::now();
        const BATCH_SIZE: usize = 10000;
        let mut total_processed = 0;

        for chunk in groups
            .iter()
            .filter(|(_k, grouped_vecs)| !AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only).is_empty())
            .collect::<Vec<_>>()
            .chunks(BATCH_SIZE)
        {
            let res = chunk
                .into_par_iter()
                .map(|(_k, grouped_vecs)| {
                    TestItemProgram::from_test_items(
                        self.is_cp,
                        self.data_source.as_str(),
                        &AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only),
                        is_pass_only,
                        product_list,
                    )
                })
                .collect::<Vec<_>>();

            total_processed += res.len();
            sender.send_batch(Some(res)).await?;
        }

        let duration = start_time.elapsed();
        log::info!(
            "Program optimized aggregation took: {:?}, is_pass_only: {}, groups size: {}, total processed: {}",
            duration,
            is_pass_only,
            groups.len(),
            total_processed
        );
        Ok(())
    }

    /// Aggregate Site groups from combined structure
    async fn aggregate_site_groups_from_combined(
        &self,
        groups: &HashMap<TestItemSiteKey, GroupedVecs>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemSite>,
    ) -> Result<(), CkProviderError> {
        let start_time = std::time::Instant::now();
        const BATCH_SIZE: usize = 10000;
        let mut total_processed = 0;

        for chunk in groups
            .iter()
            .filter(|(_k, grouped_vecs)| !AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only).is_empty())
            .collect::<Vec<_>>()
            .chunks(BATCH_SIZE)
        {
            let res = chunk
                .into_par_iter()
                .map(|(_k, grouped_vecs)| {
                    TestItemSite::from_test_items(
                        self.is_cp,
                        self.data_source.as_str(),
                        &AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only),
                        is_pass_only,
                        product_list,
                    )
                })
                .collect::<Vec<_>>();

            total_processed += res.len();
            sender.send_batch(Some(res)).await?;
        }

        let duration = start_time.elapsed();
        log::info!(
            "Site optimized aggregation took: {:?}, is_pass_only: {}, groups size: {}, total processed: {}",
            duration,
            is_pass_only,
            groups.len(),
            total_processed
        );
        Ok(())
    }

    /// Aggregate Bin groups from combined structure
    async fn aggregate_bin_groups_from_combined(
        &self,
        groups: &HashMap<TestItemBinKey, GroupedVecs>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemBin>,
    ) -> Result<(), CkProviderError> {
        let start_time = std::time::Instant::now();
        const BATCH_SIZE: usize = 10000;
        let mut total_processed = 0;

        for chunk in groups
            .iter()
            .filter(|(_k, grouped_vecs)| !AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only).is_empty())
            .collect::<Vec<_>>()
            .chunks(BATCH_SIZE)
        {
            let res = chunk
                .into_par_iter()
                .map(|(_k, grouped_vecs)| {
                    TestItemBin::from_test_items(
                        self.is_cp,
                        self.data_source.as_str(),
                        &AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only),
                        is_pass_only,
                        product_list,
                    )
                })
                .collect::<Vec<_>>();

            total_processed += res.len();
            sender.send_batch(Some(res)).await?;
        }

        let duration = start_time.elapsed();
        log::info!(
            "Bin optimized aggregation took: {:?}, is_pass_only: {}, groups size: {}, total processed: {}",
            duration,
            is_pass_only,
            groups.len(),
            total_processed
        );
        Ok(())
    }

    /// Aggregate SiteBin groups from combined structure
    async fn aggregate_site_bin_groups_from_combined(
        &self,
        groups: &HashMap<TestItemSiteBinKey, GroupedVecs>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
        sender: AsyncCkSender<TestItemSiteBin>,
    ) -> Result<(), CkProviderError> {
        let start_time = std::time::Instant::now();
        const BATCH_SIZE: usize = 10000;
        let mut total_processed = 0;

        for chunk in groups
            .iter()
            .filter(|(_k, grouped_vecs)| !AdsYmsTestItemService::get_test_items(grouped_vecs, is_pass_only).is_empty())
            .collect::<Vec<_>>()
            .chunks(BATCH_SIZE)
        {
            let res = chunk
                .into_par_iter()
                .map(|(_k, grouped_vecs)| {
                    TestItemSiteBin::from_test_items(
                        self.is_cp,
                        self.data_source.as_str(),
                        &AdsYmsTestItemService::get_test_items(&grouped_vecs, is_pass_only),
                        is_pass_only,
                        product_list,
                    )
                })
                .collect::<Vec<_>>();

            total_processed += res.len();
            sender.send_batch(Some(res)).await?;
        }

        let duration = start_time.elapsed();
        log::info!(
            "SiteBin optimized aggregation took: {:?}, is_pass_only: {}, groups size: {}, total processed: {}",
            duration,
            is_pass_only,
            groups.len(),
            total_processed
        );
        Ok(())
    }

    fn get_test_items(grouped_vecs: &GroupedVecs, is_pass_only: u8) -> &Vec<Arc<TestItemDetail>> {
        if is_pass_only == 1 {
            &grouped_vecs.pass
        } else {
            &grouped_vecs.total
        }
    }
}
