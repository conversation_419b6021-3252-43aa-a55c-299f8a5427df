use crate::config::DwTestItemConfig;
use arrow::array::RecordBatch;
use ck_provider::{
    AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, GenericStreamProcessorBuilder, StreamConfig,
};
use color_eyre::eyre::WrapErr;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::SinkHandler;
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::dwd::util::dwd_common_util::DwdCommonUtil;
use common::model::dw_table_enum::DwTableEnum;
use common::repository::mysql::lot_wafer_write_table_repository::LotWaferWriteTableRepository;
use mysql_provider::MySqlProviderImpl;
use common::parquet::RecordBatchWrapper;
use log::{error, info};
use parquet_provider::hdfs_provider::{HdfsConfig, HdfsProvider};
use parquet_provider::parquet_provider::{read_parquet, read_parquet_multi, ParquetProviderError, TempDirCleanup};
use parquet_provider::RecordBatchStreamWriter;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use uuid::Uuid;

/// Load die detail data from HDFS
pub async fn load_die_detail_data(
    die_detail_path: &str,
    config: &DwTestItemConfig,
) -> color_eyre::Result<Vec<DieDetailParquet>> {
    log::info!("开始加载die detail数据: {}", die_detail_path);

    let die_detail = read_parquet::<DieDetailParquet>(die_detail_path, Some(&config.get_hdfs_config()))
        .await
        .wrap_err("加载die detail数据失败")?;

    log::info!("成功加载die detail数据，共{}条记录", die_detail.len());
    Ok(die_detail)
}

/// Execute tombstone operations for ClickHouse cleanup
///
/// Corresponds to: CpTestItemTask.scala:49-57
pub async fn tombstone_dwd_test_item(
    mysql_provider: &MySqlProviderImpl,
    config: &DwTestItemConfig,
    customer: &str,
    factory: &str,
    test_area: &str,
    lot_id: &str,
    lot_type: &str,
    test_stage: &str,
    device_id: &str,
    wafer_no: &str,
    file_category: &str,
    sub_customer: &str,
) -> color_eyre::Result<()> {
    let lot_wafer_write_table_repository = LotWaferWriteTableRepository::new(&mysql_provider).await?;
    let write_table_record_count = lot_wafer_write_table_repository
        .count_start_write_record(
            customer,
            factory,
            test_area,
            device_id,
            lot_id,
            wafer_no,
            test_stage,
            lot_type,
            file_category,
            &DwTableEnum::DwdTestItemDetail.get_table(),
        )
        .await
        .map_err(|e| {
            let err_str = e.to_string();
            log::error!("查询写入记录数失败: {}", e);
            crate::error::DatawareError::TombstoneFailed(err_str)
        })?;

    // 2. Only execute tombstone if there are write records (lines 54-62 in Scala)
    if write_table_record_count > 0 {
        log::info!(
            "开始tombstone {} 数据 customer: {}, factory: {}, testArea: {}, deviceId: {}, lotId: {}, waferNo: {}, testStage: {}, lotType: {}",
            test_area, customer, factory, test_area, device_id, lot_id, wafer_no, test_stage, lot_type
        );

        // 1. Create TestItemDetailHandler (lines 50-51 in Scala)
        //    new TestItemDetailHandler(properties.getDwdDbName, properties.getInsertClusterTable)
        let handler = TestItemDetailHandler::new(config.dwd_db_name.clone(), config.insert_cluster_table);

        // 2. Calculate lot bucket (line 54 in Scala)
        //    DwdCommonUtil.getLotBucket(lotId, properties.getLotBucketNum)
        let lot_bucket = DwdCommonUtil::get_lot_bucket(lot_id, config.lot_bucket_num);

        // 3. Build table full name (line 55 in Scala)
        //    handler.dbName + "." + handler.tableName
        let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());

        // 4. Call CkOperate.tombstoneCk (lines 53-56 in Scala)
        CkOperate::tombstone_ck_not_check(
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            Some(lot_bucket),
            wafer_no,
            &table_full_name,
            &config.pick_random_ck_node_host(),
            &config.get_ck_address_list(),
            &config.ck_username,
            &config.ck_password,
            handler.partition_expr(),
            Some(chrono::Utc::now()),
            sub_customer,
        )
        .await
        .map_err(|e| {
            let err_str = e.to_string();
            log::error!("执行tombstone操作失败: {}", e);
            crate::error::DatawareError::TombstoneFailed(err_str)
        })?;
    } else {
        log::info!(
            "[{}, {}, {}, {}, {}, {}, {}, {}, {}] TestItemDetail不需要做tombstone",
            customer, factory, test_area, device_id, lot_id, wafer_no, test_stage, lot_type, file_category
        );
    }

    Ok(())
}

/// Load test item data from HDFS
pub async fn load_test_item_data(
    test_item_data_path: &str,
    config: &DwTestItemConfig,
) -> color_eyre::Result<Vec<Vec<TestItemDataParquet>>> {
    log::info!("开始加载test item数据: {}", test_item_data_path);

    let test_item_data =
        read_parquet_multi::<TestItemDataParquet>(test_item_data_path, Some(&config.get_hdfs_config()))
            .await
            .wrap_err("加载test item数据失败")?;

    let total_records: usize = test_item_data.iter().map(|batch| batch.len()).sum();
    log::info!("成功加载test item数据，共{}批次，{}条记录", test_item_data.len(), total_records);
    Ok(test_item_data)
}


pub async fn write_test_item_detail_to_clickhouse_concurrent(
    properties: DwTestItemConfig,
    test_item_detail: Vec<Vec<SubTestItemDetail>>,
    file_detail_map: HashMap<i64, FileDetail>,
    config: &DwTestItemConfig,
) -> Result<(Vec<Vec<SubTestItemDetail>>, HashMap<i64, FileDetail>), Box<dyn Error + Send + Sync>> {
    let ck_config = properties.get_ck_config(properties.dwd_db_name.as_str());
    let test_item_detail_handler =
        Arc::new(TestItemDetailHandler::new(properties.dwd_db_name.clone(), properties.insert_cluster_table));

    let db_table_name = format!("{}.{}", test_item_detail_handler.db_name, test_item_detail_handler.table_name);
    let batch_size = config.get_batch_size()?;

    // 配置流式处理参数 - 优化性能
    let total_batches = test_item_detail.len();
    let optimal_concurrent_flushes = std::cmp::min(total_batches * 2, 16);
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<TestItemDetailRow>(stream_config.clone(), 10);

    let ck_provider = CkProviderImpl::new(ck_config.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // 直接在这里执行并发写入，避免数据传递和克隆
    let file_detail_map_arc = Arc::new(file_detail_map);
    let test_item_detail_arc = Arc::new(test_item_detail);

    // 创建并发任务处理每个批次
    let mut tasks = Vec::new();
    for batch_idx in 0..test_item_detail_arc.len() {
        let sender_clone = sender.clone();
        let file_detail_map_clone = file_detail_map_arc.clone();
        let test_item_detail_clone = test_item_detail_arc.clone();

        let task = tokio::spawn(async move {
            // 获取信号量许可

            let test_item_detail_batch = &test_item_detail_clone[batch_idx];
            let batch_start = std::time::Instant::now();
            log::info!("开始处理批次{}写入ClickHouse，数据量: {}", batch_idx, test_item_detail_batch.len());
            let mut build_duration_total = Duration::new(0, 0);
            let mut send_duration_total = Duration::new(0, 0);
            for chunck in test_item_detail_batch.chunks(10000) {
                let build_start = std::time::Instant::now();
                let batch_rows: Vec<TestItemDetailRow> = chunck
                    .par_iter()
                    .map(|item| {
                        let file_detail = file_detail_map_clone.get(&item.FILE_ID.unwrap()).unwrap();
                        TestItemDetailRow::new(item, file_detail)
                    })
                    .collect();
                let build_duration = build_start.elapsed();
                build_duration_total += build_duration;
                let send_start = std::time::Instant::now();
                sender_clone.send_batch(Some(batch_rows)).await?;
                let send_duration = send_start.elapsed();
                send_duration_total += send_duration;
            }

            let total_duration = batch_start.elapsed();
            log::info!(
                "完成批次{}写入ClickHouse，构建耗时: {:?}, 发送耗时: {:?}, 总耗时: {:?}",
                batch_idx,
                build_duration_total,
                send_duration_total,
                total_duration
            );
            Ok::<(), Box<dyn Error + Send + Sync>>(())
        });
        tasks.push(task);
    }

    // 等待所有任务完成
    for task in tasks {
        task.await??;
    }

    // 发送结束消息
    sender.send(None).await?;

    // 等待处理器完成
    processor_handle.await?;

    // 从Arc中提取原始数据返回
    let file_detail_map = Arc::try_unwrap(file_detail_map_arc).map_err(|_| "Failed to unwrap Arc<HashMap>")?;
    let test_item_detail =
        Arc::try_unwrap(test_item_detail_arc).map_err(|_| "Failed to unwrap Arc<Vec<Vec<SubTestItemDetail>>>")?;

    Ok((test_item_detail, file_detail_map))
}


/// 结合Parquet和ClickHouse的并发写入函数
/// 同时将数据写入Parquet文件和ClickHouse数据库，实现双重输出
pub async fn write_test_item_detail_concurrent(
    file_path: &str,
    test_item_detail: Vec<Vec<SubTestItemDetail>>,
    file_detail_map: HashMap<i64, FileDetail>,
    config: &DwTestItemConfig,
    hdfs_config: Option<&HdfsConfig>,
    record_batch_size: usize,
) -> Result<(Vec<Vec<SubTestItemDetail>>, HashMap<i64, FileDetail>), Box<dyn Error + Send + Sync>> {
    info!("开始同时写入Parquet文件和ClickHouse，数据批次数: {}", test_item_detail.len());

    // 准备ClickHouse配置
    let ck_config = config.get_ck_config(config.dwd_db_name.as_str());
    let test_item_detail_handler =
        Arc::new(TestItemDetailHandler::new(config.dwd_db_name.clone(), config.insert_cluster_table));
    let db_table_name = format!("{}.{}", test_item_detail_handler.db_name, test_item_detail_handler.table_name);
    let batch_size = config.get_batch_size()?;

    // 如果有HDFS配置，先删除目标文件
    if let Some(hdfs_config) = hdfs_config {
        let hdfs_provider = HdfsProvider::new(hdfs_config.clone())?;
        hdfs_provider.delete(file_path).await?;
        info!("Successfully deleted HDFS file: {}", file_path);
    }

    if test_item_detail.is_empty() {
        return Ok((test_item_detail, file_detail_map));
    }

    // ClickHouse流配置 - 优化性能
    let total_batches = test_item_detail.len();
    let optimal_concurrent_flushes = std::cmp::min(total_batches * 2, 16);
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let ck_stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // Parquet流配置 - 串行写入优化
    let parquet_stream_config = StreamConfig::default()
        .with_buffer_size(5)
        .with_batch_size(1)
        .with_flush_interval(Duration::from_millis(100))
        .with_max_retries(0)
        .with_parallel_flush(false)
        .with_max_concurrent_flushes(1);

    // 创建两个独立的流式通道
    let (ck_sender, ck_receiver) = AsyncCkChannel::new::<TestItemDetailRow>(ck_stream_config.clone(), 10);
    let (parquet_sender, parquet_receiver) = AsyncCkChannel::new::<RecordBatch>(parquet_stream_config.clone(), 2);

    // 创建ClickHouse流处理器
    let ck_provider = CkProviderImpl::new(ck_config.clone());
    let mut ck_processor = CkStreamProcessorBuilder::new()
        .with_receiver(ck_receiver)
        .with_provider(ck_provider.clone())
        .with_config(ck_stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 准备Parquet临时文件
    let temp_uuid = Uuid::new_v4().to_string();
    let base_path = hdfs_config.map(|c| c.local_path_prefix.as_str()).unwrap_or("/tmp");
    let temp_dir = format!("{}/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", base_path, temp_uuid);
    info!("write_test_item_detail_concurrent to tmp dir: {}", temp_dir);

    let _cleanup_guard = TempDirCleanup::new(&temp_dir);
    fs::create_dir_all(&temp_dir).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
    })?;

    let file_name = "all.parquet";
    let temp_file_path = format!("{}/{}", temp_dir, file_name);

    // 创建Parquet流处理器
    let parquet_writer = RecordBatchStreamWriter::new(temp_file_path.clone())?;
    let mut parquet_processor = GenericStreamProcessorBuilder::new()
        .with_receiver(parquet_receiver)
        .with_writer(parquet_writer)
        .with_config(parquet_stream_config)
        .build()
        .map_err(|e| ParquetProviderError::RuntimeError(format!("Failed to build parquet stream processor: {}", e)))?;

    // 启动ClickHouse流处理器
    let ck_processor_handle = tokio::spawn(async move {
        if let Err(e) = ck_processor.start().await {
            eprintln!("ClickHouse流处理器错误: {:?}", e);
        }
    });

    // Parquet处理器不能在tokio::spawn中运行，因为ArrowWriter不是Send的
    // 我们将在数据发送完成后在当前线程中运行它
    // let parquet_processor_handle = parquet_processor.start();

    // 共享数据
    let file_detail_map_arc = Arc::new(file_detail_map);
    let test_item_detail_arc = Arc::new(test_item_detail);

    // 创建并发任务处理每个批次，同时发送到两个流
    let mut tasks = Vec::new();
    for batch_idx in 0..test_item_detail_arc.len() {
        let ck_sender_clone = ck_sender.clone();
        let parquet_sender_clone = parquet_sender.clone();
        let file_detail_map_clone = file_detail_map_arc.clone();
        let test_item_detail_clone = test_item_detail_arc.clone();

        let task = tokio::spawn(async move {
            let test_item_detail_batch = &test_item_detail_clone[batch_idx];
            let batch_start = std::time::Instant::now();
            info!(
                "开始处理批次{}，同时写入ClickHouse和Parquet，数据量: {}",
                batch_idx,
                test_item_detail_batch.len()
            );

            let mut ck_build_duration_total = Duration::new(0, 0);
            let mut ck_send_duration_total = Duration::new(0, 0);
            let mut parquet_build_duration_total = Duration::new(0, 0);
            let mut parquet_send_duration_total = Duration::new(0, 0);

            for parquet_chunk in test_item_detail_batch.chunks(record_batch_size) {
                // 分块处理数据
                for ck_chunk in parquet_chunk.chunks(10000) {
                    // 构建ClickHouse数据
                    let ck_build_start = std::time::Instant::now();
                    let ck_batch_rows: Vec<TestItemDetailRow> = ck_chunk
                        .par_iter()
                        .map(|item| {
                            let file_detail = file_detail_map_clone.get(&item.FILE_ID.unwrap()).unwrap();
                            TestItemDetailRow::new(item, file_detail)
                        })
                        .collect();
                    let ck_build_duration = ck_build_start.elapsed();
                    ck_build_duration_total += ck_build_duration;

                    // 发送到ClickHouse流
                    let ck_send_start = std::time::Instant::now();
                    ck_sender_clone.send_batch(Some(ck_batch_rows)).await?;
                    let ck_send_duration = ck_send_start.elapsed();
                    ck_send_duration_total += ck_send_duration;
                }

                let parquet_build_start = std::time::Instant::now();
                let parquet_chunk_vec = parquet_chunk.to_vec();
                let record_batch = SubTestItemDetail::to_record_batch(&parquet_chunk_vec)
                    .map_err(|e| format!("Failed to convert data to record batch: {}", e))?;
                let parquet_build_duration = parquet_build_start.elapsed();
                parquet_build_duration_total += parquet_build_duration;

                // 发送到Parquet流
                let parquet_send_start = std::time::Instant::now();
                parquet_sender_clone
                    .send_batch(Some(vec![record_batch]))
                    .await
                    .map_err(|e| format!("Failed to send parquet batch: {}", e))?;
                let parquet_send_duration = parquet_send_start.elapsed();
                parquet_send_duration_total += parquet_send_duration;
            }

            let total_duration = batch_start.elapsed();
            info!(
                "完成批次{}双重写入 - CK构建: {:?}, CK发送: {:?}, Parquet构建: {:?}, Parquet发送: {:?}, 总耗时: {:?}",
                batch_idx,
                ck_build_duration_total,
                ck_send_duration_total,
                parquet_build_duration_total,
                parquet_send_duration_total,
                total_duration
            );
            Ok::<(), Box<dyn Error + Send + Sync>>(())
        });

        tasks.push(task);
    }

    let send_handle = tokio::spawn(async move {
        // 等待所有生产者任务完成
        for task in tasks {
            if let Err(e) = task.await {
                log::error!("Task join error: {}", e);
            }
        }

        // 发送结束信号到两个流
        if let Err(e) = ck_sender.send(None).await {
            log::error!("Failed to send CK end signal: {}", e)
        };

        if let Err(e) = parquet_sender.send(None).await {
            log::error!("Failed to send Parquet end signal: {}", e)
        };
    });

    // 在当前线程中运行Parquet处理器
    // 启动ClickHouse流处理器
    tokio::spawn(async move {
        if let Err(e) =parquet_processor.start().await {
            eprintln!("ClickHouse流处理器错误: {:?}", e);
        }
    });


    // let parquet_result = parquet_processor_handle.await;
    // parquet_result.map_err(|e| format!("Parquet stream processor error: {}", e))?;

    info!("Successfully completed concurrent writing to ClickHouse and Parquet temp file: {}", temp_file_path);

    // 如果有HDFS配置，上传文件
    if let Some(hdfs_config) = hdfs_config {
        let hdfs_provider = HdfsProvider::new(hdfs_config.clone())?;
        hdfs_provider.upload(&temp_file_path, file_path).await?;
        info!("Successfully uploaded parquet file to HDFS: {}", file_path);
    }

    send_handle
        .await
        .map_err(|e| ParquetProviderError::RuntimeError(format!("Send handle error: {}", e)))?;

    // 等待ClickHouse处理器完成
    ck_processor_handle.await?;

    // 从Arc中提取原始数据返回
    let file_detail_map = Arc::try_unwrap(file_detail_map_arc).map_err(|_| "Failed to unwrap Arc<HashMap>")?;
    let test_item_detail =
        Arc::try_unwrap(test_item_detail_arc).map_err(|_| "Failed to unwrap Arc<Vec<Vec<SubTestItemDetail>>>")?;

    Ok((test_item_detail, file_detail_map))
}
