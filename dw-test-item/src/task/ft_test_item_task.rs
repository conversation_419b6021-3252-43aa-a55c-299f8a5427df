use bumpalo::Bump;
use color_eyre::{eyre::WrapErr, Result};
use common::model::dws_mode::DwsMode;
use std::time::Instant;

use crate::config::DwTestItemConfig;
use crate::service::ads::FtYmsAdsTestItemService;
use crate::service::dim::ft_dim_test_item_service::FtDimTestItemService;
use crate::service::ft_dws_test_item_service::FtDwsTestItemService;
use crate::task::ft_task_params::FtTaskParams;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;

use crate::cpu_limit::init_cpu_limit;
use crate::service::dwd::dwd_service::{load_die_detail_data, load_test_item_data, tombstone_dwd_test_item};
use crate::service::dwd::ft_dwd_test_item_service::FtDwdTestItemService;
use common::model::constant;
use common::model::constant::run_mode::RunMode;
use common::model::constant::EMPTY;
use common::model::key::wafer_key::WaferKey;
use common::utils::path;
use mysql_provider::MySqlProviderImpl;
// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/task/impl/FtTestItemTask.scala

/// FtTestItemTask orchestrates the complete FT test item data processing pipeline
/// This is the main entry point for FT stage data warehouse processing
#[derive(Debug)]
pub struct FtTestItemTask {
    config: DwTestItemConfig,
}

impl FtTestItemTask {
    /// Create new FtTestItemTask with configuration
    pub fn new() -> Self {
        let config = DwTestItemConfig::get_config().unwrap();
        Self { config }
    }

    /// Execute the complete FT test item task
    ///
    /// Corresponds to: FtTestItemTask.scala doTask method
    /// private def doTask(customer: String, subCustomer: String, factory: String, factorySite: String,
    ///                   testArea: String, lotId: String, waferNo: String, lotType: String, deviceId: String,
    ///                   testStage: String, mode: String, fileCategory: String, ckSinkType: String,
    ///                   testItemDetailResultPartition: String, dataVersion: String, calculateYmsTestItem: String): Unit
    pub async fn do_task(&self, params: FtTaskParams) -> Result<()> {
        // 首先设置CPU限制
        init_cpu_limit(self.config.max_threads);

        let start_time = Instant::now();
        log::info!("ft test item task start...");
        let bump = Bump::new();
        let mysql_provider = MySqlProviderImpl::new(self.config.get_mysql_config()).await?;

        // Tombstone操作耗时统计
        let tombstone_start = Instant::now();
        tombstone_dwd_test_item(
            &mysql_provider,
            &self.config,
            &params.customer,
            &params.factory,
            &params.test_area,
            &params.lot_id,
            &params.lot_type,
            &params.test_stage,
            &params.device_id,
            EMPTY,
            &params.file_category,
            &params.sub_customer,
        )
        .await
        .wrap_err("执行tombstone操作失败")?;
        let tombstone_duration = tombstone_start.elapsed();
        log::info!("tombstone操作完成，耗时：[{:?}]", tombstone_duration);

        // FT使用die_detail_result_dir而不是cp_die_detail_result_dir，并且使用lot路径而不是wafer路径
        let die_detail_path = path::get_dwd_lot_path(
            &self.config.die_detail_result_dir,
            &params.test_area,
            &params.customer,
            &params.factory,
            &params.lot_id,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );

        // FT使用ft_result_dir而不是cp_result_dir
        let base_path = if self.config.ft_result_dir.ends_with(constant::SLASH) {
            self.config.ft_result_dir.clone()
        } else {
            format!("{}{}", self.config.ft_result_dir, constant::SLASH)
        };

        let test_item_data_path = path::get_ods_lot_path(
            &base_path,
            &params.file_category,
            &params.test_area,
            &params.customer,
            &params.factory,
            constant::TEST_ITEM_DATA,
            &params.lot_id,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );

        log::info!("当前Task需要计算的数据集: [DieDetail={}], [TestItemData={}]", die_detail_path, test_item_data_path);

        // 数据加载耗时统计
        let data_load_start = Instant::now();
        let die_detail = load_die_detail_data(&die_detail_path, &self.config).await?;
        let die_detail_load_duration = data_load_start.elapsed();
        log::info!("die_detail数据加载完成，耗时：[{:?}]", die_detail_load_duration);

        let test_item_load_start = Instant::now();
        let test_item_data: Vec<Vec<TestItemDataParquet>> = load_test_item_data(&test_item_data_path, &self.config).await?;
        let test_item_load_duration = test_item_load_start.elapsed();
        log::info!("test_item_data数据加载完成，耗时：[{:?}]", test_item_load_duration);

        // 计算dwd test item
        log::info!("开始计算DWD层数据...");
        let dwd_start = Instant::now();
        let wafer_key = WaferKey::from(&params);
        let dwd_calculate_result = bump.alloc(
            FtDwdTestItemService::new(self.config.test_item_detail_result_partition, params.test_area.clone())
                .calculate(
                    die_detail,
                    test_item_data,
                    &wafer_key,
                    params.test_area.clone(),
                    params.execute_mode.clone(),
                    params.file_category.clone(),
                    params.ck_sink_type.clone(),
                    &RunMode::RUST.to_string(),
                    &self.config,
                    &mysql_provider
                )
                .await
                .map_err(|e| {
                    let err_str = e.to_string();
                    log::error!("计算dwd test item失败: {}", e);
                    crate::error::DatawareError::DwdCalculateFailed(err_str)
                })
                .wrap_err("计算dwd test item失败")?,
        );
        let dwd_duration = dwd_start.elapsed();
        log::info!("DWD层数据计算完成，耗时：[{:?}]", dwd_duration);

        // 计算dws test item
        log::info!("开始计算DWS层数据...");
        let dws_start = Instant::now();

        FtDwsTestItemService::new(self.config.dws_result_partition, params.test_area.clone(), self.config.clone())
            .calculate(
                &dwd_calculate_result.die_detail,
                &dwd_calculate_result.test_item_detail,
                &wafer_key,
                &params.test_area,
                DwsMode::DWS.get_mode(),
                &params.execute_mode,
                &RunMode::RUST.to_string(),
                &mysql_provider
            )
            .await
            .map_err(|e| {
                let err_str = e.to_string();
                log::error!("计算dws test item失败: {}", e);
                crate::error::DatawareError::DwsCalculateFailed(err_str)
            })
            .wrap_err("计算dws test item失败")?;

        let dws_duration = dws_start.elapsed();
        log::info!("DWS层数据计算完成，耗时：[{:?}]", dws_duration);

        // 计算 DIM 层数据
        log::info!("开始计算DIM层数据...");
        let dim_start = Instant::now();
        FtDimTestItemService::new(self.config.clone())
            .calculate(
                &dwd_calculate_result.test_item_detail,
                &dwd_calculate_result.file_detail_map,
                wafer_key,
                &params.test_area,
                &params.execute_mode,
                &params.file_category,
                &RunMode::RUST.to_string(),
                &mysql_provider
            )
            .await
            .map_err(|e| {
                let err_str = e.to_string();
                log::error!("计算 DIM 层数据失败: {}", e);
                crate::error::DatawareError::DimCalculateFailed(err_str)
            })
            .wrap_err("计算 DIM 层数据失败")?;

        let dim_duration = dim_start.elapsed();
        log::info!("DIM层数据计算完成，耗时：[{:?}]", dim_duration);

        // 计算ADS YMS test item
        log::info!("开始计算ADS层数据...");
        let ads_start = Instant::now();
        FtYmsAdsTestItemService::new(self.config.clone())
            .calculate(
                &dwd_calculate_result.file_detail_map,
                &dwd_calculate_result.test_item_detail,
                &params.customer,
                &params.factory,
                &params.test_area,
                &params.device_id,
                &params.lot_type,
                &params.test_stage,
                &params.lot_id,
                EMPTY,
                &params.data_version,
                &params.file_category,
                &params.calculate_yms_test_item,
            )
            .await
            .map_err(|e| {
                let err_str = e.to_string();
                log::error!("计算ads yms test item失败: {}", e);
                crate::error::DatawareError::AdsCalculateFailed(err_str)
            })
            .wrap_err("计算ads yms test item失败")?;

        let ads_duration = ads_start.elapsed();
        log::info!("ADS层数据计算完成，耗时：[{:?}]", ads_duration);

        drop(bump);
        let duration = start_time.elapsed();
        log::info!("ft test item task end...任务耗时：[{:?}]", duration);
        Ok(())
    }
}
