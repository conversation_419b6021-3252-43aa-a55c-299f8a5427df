use crate::parquet::RecordBatchWrapper;
use arrow::datatypes::{DataType, Field, FieldRef};
use arrow::record_batch::RecordBatch;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_arrow::schema::SchemaLike;
use serde_arrow::schema::TracingOptions;
use std::sync::Arc;

/// Bin测试项索引HDFS数据结构
/// 对应Scala中的BinTestItemIndex case class，用于HDFS存储
#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct BinTestItemIndex {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub LOT_TYPE: String,
    pub DEVICE_ID: String,
    pub LOT_ID: String,
    pub WAFER_ID: String,
    pub WAFER_ID_KEY: String,
    pub WAFER_NO: String,
    pub WAFER_NO_KEY: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_NUM_KEY: String,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_NUM_KEY: String,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub TESTITEM_TYPE: String,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub HI_SPEC: Option<f64>,
    pub LO_SPEC: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub LO_LIMIT: Option<f64>,
    pub FIRST_PASS_CNT: Option<i64>,
    pub FINAL_PASS_CNT: Option<i64>,
    pub FIRST_FAIL_CNT: Option<i64>,
    pub FINAL_FAIL_CNT: Option<i64>,
    pub TOTAL_CNT: Option<i64>,
    pub FIRST_MEAN: Option<f64>,
    pub FINAL_MEAN: Option<f64>,
    pub FIRST_SUM: Option<f64>,
    pub FINAL_SUM: Option<f64>,
    pub FIRST_STANDARD_SQUARE_SUM: Option<f64>,
    pub FINAL_STANDARD_SQUARE_SUM: Option<f64>,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    pub CREATE_TIME: i64,
    pub CREATE_USER: String,
    pub VERSION: i64,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

impl RecordBatchWrapper for BinTestItemIndex {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<BinTestItemIndex> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }
        // 使用DWD层的字段处理逻辑
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<BinTestItemIndex>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create schema from samples: {}", e))?;

        // 定义需要特殊处理的decimal字段
        let decimal_fields = vec![
            "HI_SPEC",
            "LO_SPEC",
            "HI_LIMIT",
            "LO_LIMIT",
            "FIRST_MEAN",
            "FINAL_MEAN",
            "FIRST_SUM",
            "FINAL_SUM",
            "FIRST_STANDARD_SQUARE_SUM",
            "FINAL_STANDARD_SQUARE_SUM",
        ];

        // 重新映射字段类型
        let new_fields = fields
            .into_iter()
            .map(|v| {
                if decimal_fields.contains(&v.name().as_str()) {
                    Arc::new(Field::new(v.name(), DataType::Decimal128(38, 18), true))
                } else {
                    v
                }
            })
            .collect::<Vec<_>>();

        let record_batch = serde_arrow::to_record_batch(&new_fields, &data)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))?;
        Ok(record_batch)
    }
}

/// Bin测试项索引子结构
/// 对应Scala中的SubBinTestItemIndex case class
#[derive(Debug, Clone, Default)]
pub struct SubBinTestItemIndex {
    pub tester_names: String,
    pub tester_types: String,
    pub prober_handler_ids: String,
    pub prober_card_load_board_ids: String,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub start_hour_key: String,
    pub start_day_key: String,
    pub end_hour_key: String,
    pub end_day_key: String,
    pub first_pass_cnt: i64,
    pub final_pass_cnt: i64,
    pub first_fail_cnt: i64,
    pub final_fail_cnt: i64,
    pub total_cnt: i64,
    pub first_mean: Option<f64>,
    pub final_mean: Option<f64>,
    pub first_sum: Option<f64>,
    pub final_sum: Option<f64>,
    pub first_standard_square_sum: Option<f64>,
    pub final_standard_square_sum: Option<f64>,
}
