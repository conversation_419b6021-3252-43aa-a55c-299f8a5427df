use arrow::array::{Int32Array, StringArray};
use arrow::datatypes::{DataType, Field, Schema};
use arrow::record_batch::RecordBatch;
use common::parquet::RecordBatchWrapper;
use parquet_provider::parquet_provider::{write_parquet_stream, ParquetProviderError};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// 示例数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SampleData {
    pub id: i32,
    pub name: String,
    pub value: Option<i32>,
}

impl RecordBatchWrapper for SampleData {
    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String> {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // 定义 schema
        let schema = Arc::new(Schema::new(vec![
            Field::new("id", DataType::Int32, false),
            Field::new("name", DataType::Utf8, false),
            Field::new("value", DataType::Int32, true),
        ]));

        // 提取数据
        let ids: Vec<i32> = data.iter().map(|item| item.id).collect();
        let names: Vec<String> = data.iter().map(|item| item.name.clone()).collect();
        let values: Vec<Option<i32>> = data.iter().map(|item| item.value).collect();

        // 创建 Arrow 数组
        let id_array = Arc::new(Int32Array::from(ids));
        let name_array = Arc::new(StringArray::from(names));
        let value_array = Arc::new(Int32Array::from(values));

        // 创建 RecordBatch
        let batch = RecordBatch::try_new(
            schema,
            vec![id_array, name_array, value_array],
        ).map_err(|e| e.to_string())?;

        Ok(batch)
    }

    fn from_record_batch(_batch: &RecordBatch) -> Result<Vec<Self>, String> {
        // 这个示例中我们不需要从 RecordBatch 转换回数据
        // 所以提供一个简单的实现
        Err("from_record_batch not implemented for this example".to_string())
    }
}

#[tokio::main]
async fn main() -> Result<(), ParquetProviderError> {
    // 初始化日志
    env_logger::init();

    // 创建示例数据
    let data_sources = create_sample_data();

    // 输出文件路径
    let output_path = "/tmp/deploy/onedata/dataware/dataware-dw-test-item/data/write/stream_example.parquet";

    println!("开始流式写入 Parquet 文件...");
    println!("输出路径: {}", output_path);
    println!("数据批次数量: {}", data_sources.len());

    // 执行流式写入
    write_parquet_stream(
        output_path,
        data_sources,
        None, // 不使用 HDFS
        1000, // record_batch_size
    )
    .await?;

    println!("流式写入完成！");
    println!("文件已保存到: {}", output_path);

    Ok(())
}

/// 创建示例数据
fn create_sample_data() -> Vec<Vec<SampleData>> {
    let mut data_sources = Vec::new();

    // 创建多个数据批次
    for batch_idx in 0..10 {
        let batch_size = 1000;
        let start_id = batch_idx * batch_size;

        let mut batch_data = Vec::new();
        for i in start_id..start_id + batch_size {
            let sample = SampleData {
                id: i,
                name: format!("name_{}", i),
                value: if i % 10 == 0 { None } else { Some(i * 2) },
            };
            batch_data.push(sample);
        }

        data_sources.push(batch_data);
        println!("创建数据批次 {}: {} 条记录", batch_idx, batch_size);
    }

    data_sources
}
